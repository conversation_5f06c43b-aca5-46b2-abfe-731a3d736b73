import openpyxl
import csv

# 读取XLSX文件
def read_xlsx(file_path):
    workbook = openpyxl.load_workbook(file_path)
    sheet = workbook.active
    data = []
    for row in sheet.iter_rows(values_only=True):
        data.append(row)
    return data

# 写入CSV文件
def write_csv(data, output_path):
    with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(data)

# 主函数
if __name__ == "__main__":
    input_file = "d:\\kjpt\\report\\data\\指标主题.xlsx"
    output_file = "d:\\kjpt\\report\\data\\指标主题.csv"
    
    # 读取XLSX文件
    data = read_xlsx(input_file)
    
    # 写入CSV文件
    write_csv(data, output_file)
    
    print(f"文件已成功转换为 {output_file}")