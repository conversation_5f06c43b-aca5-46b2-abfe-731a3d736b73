from typing import Annotated, Any, Dict, Sequence
from datetime import date, timedelta, datetime
from typing_extensions import TypedDict, Optional
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import ToolNode
from langgraph.graph import END, StateGraph, START, MessagesState




class AgentState(MessagesState):
    text: Annotated[str, "用户输入"]
    rewrite: Annotated[str, "用户输入重写"]
    
    



class InitialState:
    """Handles state initialization and propagation through the graph."""

    def __init__(self, max_recur_limit=50):
        """Initialize with configuration parameters."""
        self.max_recur_limit = max_recur_limit

    def create_initial_state(
        self, company_name: str,symbol:str, trade_date: str
    ) -> Dict[str, Any]:
        """Create the initial state for the agent graph."""
        return {
            "messages": [("human", company_name+symbol)],
            "company_name": company_name,
            "symbol": symbol,
            "trade_date": str(trade_date),
            "debate_research_state": DebateReserchState(
                {"history": "", "current_response": "", "count": 0}
            ),
            "fundamentals_report": "",
            "sentiment_report": "",
            "news_report": "",
        }

    def get_graph_args(self) -> Dict[str, Any]:
        """Get arguments for the graph invocation."""
        return {
            "stream_mode": "values",
            "config": {"recursion_limit": self.max_recur_limit},
        }